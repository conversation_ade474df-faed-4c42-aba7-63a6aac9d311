import 'package:flutter/material.dart';
import 'dart:async';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  double _opacity = 0.0;
  double _scale = 0.5;
  bool _showLoading = false;

  @override
  void initState() {
    super.initState();
    _animate();
  }

  void _animate() async {
    await Future.delayed(const Duration(milliseconds: 300));
    setState(() => _opacity = 1.0);
    await Future.delayed(const Duration(milliseconds: 300));
    setState(() => _scale = 1.0);
    await Future.delayed(const Duration(milliseconds: 500));
    setState(() => _showLoading = true);
    await Future.delayed(const Duration(seconds: 2));
    
    // Simulate async data loading
    await Future.delayed(const Duration(seconds: 1));
    
    // Navigate to dashboard
    if (mounted) {
      Navigator.of(context).pushReplacementNamed('/dashboard');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.primary,
      body: Center(
        child: AnimatedOpacity(
          opacity: _opacity,
          duration: const Duration(milliseconds: 500),
          child: AnimatedScale(
            scale: _scale,
            duration: const Duration(milliseconds: 500),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AnimatedOpacity(
                  opacity: _showLoading ? 0.0 : 1.0,
                  duration: const Duration(milliseconds: 300),
                  child: Icon(
                    Icons.home,
                    size: 120,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 32),
                AnimatedOpacity(
                  opacity: _showLoading ? 0.0 : 1.0,
                  duration: const Duration(milliseconds: 300),
                  child: Text(
                    'Real Estate',
                    style: TextStyle(
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      letterSpacing: 2,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                AnimatedOpacity(
                  opacity: _showLoading ? 0.0 : 1.0,
                  duration: const Duration(milliseconds: 300),
                  child: Text(
                    'Find Your Dream Home',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.white70,
                      letterSpacing: 1,
                    ),
                  ),
                ),
                const SizedBox(height: 32),
                AnimatedOpacity(
                  opacity: _showLoading ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 300),
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 3,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
