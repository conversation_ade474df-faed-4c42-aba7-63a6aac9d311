import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';

class UserProvider extends ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  // State variables
  User? _currentUser;
  bool _isLoading = false;
  bool _isAuthenticated = false;
  bool _hasError = false;
  String _errorMessage = '';
  
  // Getters
  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _isAuthenticated;
  bool get hasError => _hasError;
  String get errorMessage => _errorMessage;
  
  // Initialize provider
  Future<void> initialize() async {
    await loadCurrentUser();
  }
  
  // Load current user
  Future<void> loadCurrentUser() async {
    _setLoading(true);
    _clearError();
    
    try {
      // First try to load from local storage
      _currentUser = StorageService.getCurrentUser();
      
      if (_currentUser != null) {
        _isAuthenticated = true;
        notifyListeners();
        
        // Then try to sync with server
        try {
          final serverUser = await _apiService.getCurrentUser();
          if (serverUser != null) {
            _currentUser = serverUser;
            await StorageService.saveUser(_currentUser!);
            notifyListeners();
          }
        } catch (e) {
          // Server sync failed, but we have local user data
          print('Failed to sync user data: $e');
        }
      } else {
        // No local user, try to get from server
        _currentUser = await _apiService.getCurrentUser();
        if (_currentUser != null) {
          _isAuthenticated = true;
          await StorageService.saveUser(_currentUser!);
        }
      }
    } catch (e) {
      _setError('Failed to load user: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }
  
  // Update user profile
  Future<bool> updateProfile({
    String? name,
    String? email,
    String? phone,
    String? profileImage,
  }) async {
    if (_currentUser == null) return false;
    
    _setLoading(true);
    _clearError();
    
    try {
      final updatedUser = _currentUser!.copyWith(
        name: name ?? _currentUser!.name,
        email: email ?? _currentUser!.email,
        phone: phone ?? _currentUser!.phone,
        profileImage: profileImage ?? _currentUser!.profileImage,
      );
      
      final success = await _apiService.updateUserProfile(updatedUser);
      
      if (success) {
        _currentUser = updatedUser;
        await StorageService.saveUser(_currentUser!);
        notifyListeners();
        return true;
      } else {
        _setError('Failed to update profile');
        return false;
      }
    } catch (e) {
      _setError('Failed to update profile: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // Update user preferences
  Future<bool> updatePreferences(UserPreferences preferences) async {
    if (_currentUser == null) return false;
    
    _setLoading(true);
    _clearError();
    
    try {
      final updatedUser = _currentUser!.copyWith(preferences: preferences);
      
      final success = await _apiService.updateUserProfile(updatedUser);
      
      if (success) {
        _currentUser = updatedUser;
        await StorageService.saveUser(_currentUser!);
        
        // Update local preferences
        await StorageService.setDarkMode(preferences.darkMode);
        await StorageService.setNotificationsEnabled(preferences.notifications);
        await StorageService.setLanguage(preferences.language);
        await StorageService.setCurrency(preferences.currency);
        
        notifyListeners();
        return true;
      } else {
        _setError('Failed to update preferences');
        return false;
      }
    } catch (e) {
      _setError('Failed to update preferences: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // Add property to favorites
  Future<bool> addToFavorites(String propertyId) async {
    if (_currentUser == null) return false;
    
    try {
      final success = await _apiService.addToFavorites(propertyId);
      
      if (success) {
        final updatedFavorites = List<String>.from(_currentUser!.favoritePropertyIds);
        if (!updatedFavorites.contains(propertyId)) {
          updatedFavorites.add(propertyId);
          _currentUser = _currentUser!.copyWith(favoritePropertyIds: updatedFavorites);
          await StorageService.saveUser(_currentUser!);
          await StorageService.addToFavorites(propertyId);
          notifyListeners();
        }
        return true;
      } else {
        _setError('Failed to add to favorites');
        return false;
      }
    } catch (e) {
      _setError('Failed to add to favorites: ${e.toString()}');
      return false;
    }
  }
  
  // Remove property from favorites
  Future<bool> removeFromFavorites(String propertyId) async {
    if (_currentUser == null) return false;
    
    try {
      final success = await _apiService.removeFromFavorites(propertyId);
      
      if (success) {
        final updatedFavorites = List<String>.from(_currentUser!.favoritePropertyIds);
        updatedFavorites.remove(propertyId);
        _currentUser = _currentUser!.copyWith(favoritePropertyIds: updatedFavorites);
        await StorageService.saveUser(_currentUser!);
        await StorageService.removeFromFavorites(propertyId);
        notifyListeners();
        return true;
      } else {
        _setError('Failed to remove from favorites');
        return false;
      }
    } catch (e) {
      _setError('Failed to remove from favorites: ${e.toString()}');
      return false;
    }
  }
  
  // Check if property is favorite
  bool isFavorite(String propertyId) {
    return _currentUser?.favoritePropertyIds.contains(propertyId) ?? false;
  }
  
  // Get favorite property IDs
  List<String> getFavoriteIds() {
    return _currentUser?.favoritePropertyIds ?? [];
  }
  
  // Login (mock implementation)
  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _clearError();
    
    try {
      // Mock login - in real app, this would call authentication API
      await Future.delayed(const Duration(seconds: 2));
      
      // For demo, create a mock user
      _currentUser = User(
        id: 'user_${DateTime.now().millisecondsSinceEpoch}',
        name: 'John Doe',
        email: email,
        phone: '+****************',
        profileImage: null,
        role: UserRole.buyer,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        favoritePropertyIds: [],
        preferences: UserPreferences(),
      );
      
      _isAuthenticated = true;
      await StorageService.saveUser(_currentUser!);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Login failed: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // Register (mock implementation)
  Future<bool> register(String name, String email, String password, String phone) async {
    _setLoading(true);
    _clearError();
    
    try {
      // Mock registration - in real app, this would call registration API
      await Future.delayed(const Duration(seconds: 2));
      
      _currentUser = User(
        id: 'user_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        email: email,
        phone: phone,
        profileImage: null,
        role: UserRole.buyer,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        favoritePropertyIds: [],
        preferences: UserPreferences(),
      );
      
      _isAuthenticated = true;
      await StorageService.saveUser(_currentUser!);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Registration failed: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // Logout
  Future<void> logout() async {
    _setLoading(true);
    
    try {
      // Clear user data
      await StorageService.clearUser();
      _currentUser = null;
      _isAuthenticated = false;
      notifyListeners();
    } catch (e) {
      _setError('Logout failed: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }
  
  // Utility methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void _setError(String message) {
    _hasError = true;
    _errorMessage = message;
    notifyListeners();
  }
  
  void _clearError() {
    _hasError = false;
    _errorMessage = '';
    notifyListeners();
  }
  
  // Get user display name
  String getDisplayName() {
    return _currentUser?.name ?? 'Guest User';
  }
  
  // Get user initials for avatar
  String getUserInitials() {
    if (_currentUser?.name == null) return 'GU';
    
    final nameParts = _currentUser!.name.split(' ');
    if (nameParts.length >= 2) {
      return '${nameParts[0][0]}${nameParts[1][0]}'.toUpperCase();
    } else if (nameParts.isNotEmpty) {
      return nameParts[0].substring(0, 2).toUpperCase();
    }
    return 'GU';
  }
  
  // Check if user has specific role
  bool hasRole(UserRole role) {
    return _currentUser?.role == role;
  }
  
  // Get user role display name
  String getRoleDisplayName() {
    return _currentUser?.role.displayName ?? 'Guest';
  }
  
  // Export user data
  Map<String, dynamic> exportUserData() {
    return StorageService.exportUserData();
  }
  
  // Import user data
  Future<void> importUserData(Map<String, dynamic> data) async {
    await StorageService.importUserData(data);
    await loadCurrentUser();
  }
}
