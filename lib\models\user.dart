import 'package:hive/hive.dart';

part 'user.g.dart';

@HiveType(typeId: 3)
class User extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String email;

  @HiveField(3)
  final String phone;

  @HiveField(4)
  final String? profileImage;

  @HiveField(5)
  final UserRole role;

  @HiveField(6)
  final DateTime createdAt;

  @HiveField(7)
  final DateTime lastLoginAt;

  @HiveField(8)
  final List<String> favoritePropertyIds;

  @HiveField(9)
  final UserPreferences preferences;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    this.profileImage,
    required this.role,
    required this.createdAt,
    required this.lastLoginAt,
    required this.favoritePropertyIds,
    required this.preferences,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      profileImage: json['profileImage'],
      role: UserRole.values.firstWhere(
        (e) => e.toString().split('.').last == json['role'],
        orElse: () => UserRole.buyer,
      ),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      lastLoginAt: DateTime.parse(json['lastLoginAt'] ?? DateTime.now().toIso8601String()),
      favoritePropertyIds: List<String>.from(json['favoritePropertyIds'] ?? []),
      preferences: UserPreferences.fromJson(json['preferences'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'profileImage': profileImage,
      'role': role.toString().split('.').last,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt.toIso8601String(),
      'favoritePropertyIds': favoritePropertyIds,
      'preferences': preferences.toJson(),
    };
  }

  User copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? profileImage,
    UserRole? role,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    List<String>? favoritePropertyIds,
    UserPreferences? preferences,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      profileImage: profileImage ?? this.profileImage,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      favoritePropertyIds: favoritePropertyIds ?? this.favoritePropertyIds,
      preferences: preferences ?? this.preferences,
    );
  }
}

@HiveType(typeId: 4)
enum UserRole {
  @HiveField(0)
  buyer,
  @HiveField(1)
  seller,
  @HiveField(2)
  agent,
  @HiveField(3)
  admin,
}

@HiveType(typeId: 5)
class UserPreferences extends HiveObject {
  @HiveField(0)
  final bool darkMode;

  @HiveField(1)
  final bool notifications;

  @HiveField(2)
  final String currency;

  @HiveField(3)
  final String language;

  @HiveField(4)
  final double minPrice;

  @HiveField(5)
  final double maxPrice;

  @HiveField(6)
  final List<String> preferredLocations;

  @HiveField(7)
  final List<PropertyType> preferredPropertyTypes;

  UserPreferences({
    this.darkMode = false,
    this.notifications = true,
    this.currency = 'USD',
    this.language = 'en',
    this.minPrice = 0,
    this.maxPrice = 10000000,
    this.preferredLocations = const [],
    this.preferredPropertyTypes = const [],
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      darkMode: json['darkMode'] ?? false,
      notifications: json['notifications'] ?? true,
      currency: json['currency'] ?? 'USD',
      language: json['language'] ?? 'en',
      minPrice: (json['minPrice'] ?? 0).toDouble(),
      maxPrice: (json['maxPrice'] ?? 10000000).toDouble(),
      preferredLocations: List<String>.from(json['preferredLocations'] ?? []),
      preferredPropertyTypes: (json['preferredPropertyTypes'] as List<dynamic>?)
              ?.map((e) => PropertyType.values.firstWhere(
                    (type) => type.toString().split('.').last == e,
                    orElse: () => PropertyType.house,
                  ))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'darkMode': darkMode,
      'notifications': notifications,
      'currency': currency,
      'language': language,
      'minPrice': minPrice,
      'maxPrice': maxPrice,
      'preferredLocations': preferredLocations,
      'preferredPropertyTypes': preferredPropertyTypes.map((e) => e.toString().split('.').last).toList(),
    };
  }

  UserPreferences copyWith({
    bool? darkMode,
    bool? notifications,
    String? currency,
    String? language,
    double? minPrice,
    double? maxPrice,
    List<String>? preferredLocations,
    List<PropertyType>? preferredPropertyTypes,
  }) {
    return UserPreferences(
      darkMode: darkMode ?? this.darkMode,
      notifications: notifications ?? this.notifications,
      currency: currency ?? this.currency,
      language: language ?? this.language,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      preferredLocations: preferredLocations ?? this.preferredLocations,
      preferredPropertyTypes: preferredPropertyTypes ?? this.preferredPropertyTypes,
    );
  }
}

extension UserRoleExtension on UserRole {
  String get displayName {
    switch (this) {
      case UserRole.buyer:
        return 'Buyer';
      case UserRole.seller:
        return 'Seller';
      case UserRole.agent:
        return 'Agent';
      case UserRole.admin:
        return 'Admin';
    }
  }
}
