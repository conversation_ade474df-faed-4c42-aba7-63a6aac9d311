import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../services/storage_service.dart';

class AppProvider extends ChangeNotifier {
  // Theme and appearance
  bool _isDarkMode = false;
  ThemeMode _themeMode = ThemeMode.system;
  
  // App state
  bool _isFirstLaunch = true;
  bool _isInitialized = false;
  bool _isLoading = false;
  String _currentLanguage = 'en';
  String _currentCurrency = 'USD';
  
  // Navigation state
  int _currentBottomNavIndex = 0;
  
  // Connectivity and sync
  bool _isOnline = true;
  bool _isSyncing = false;
  DateTime? _lastSyncTime;
  
  // Notifications
  bool _notificationsEnabled = true;
  List<AppNotification> _notifications = [];
  
  // Getters
  bool get isDarkMode => _isDarkMode;
  ThemeMode get themeMode => _themeMode;
  bool get isFirstLaunch => _isFirstLaunch;
  bool get isInitialized => _isInitialized;
  bool get isLoading => _isLoading;
  String get currentLanguage => _currentLanguage;
  String get currentCurrency => _currentCurrency;
  int get currentBottomNavIndex => _currentBottomNavIndex;
  bool get isOnline => _isOnline;
  bool get isSyncing => _isSyncing;
  DateTime? get lastSyncTime => _lastSyncTime;
  bool get notificationsEnabled => _notificationsEnabled;
  List<AppNotification> get notifications => _notifications;
  
  // Initialize app
  Future<void> initialize() async {
    _setLoading(true);
    
    try {
      // Initialize storage
      await StorageService.init();
      
      // Load app preferences
      await _loadPreferences();
      
      _isInitialized = true;
    } catch (e) {
      print('Error initializing app: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // Load preferences from storage
  Future<void> _loadPreferences() async {
    _isDarkMode = StorageService.isDarkMode();
    _isFirstLaunch = StorageService.isFirstLaunch();
    _notificationsEnabled = StorageService.areNotificationsEnabled();
    _currentLanguage = StorageService.getLanguage();
    _currentCurrency = StorageService.getCurrency();
    
    // Set theme mode based on dark mode preference
    _themeMode = _isDarkMode ? ThemeMode.dark : ThemeMode.light;
    
    notifyListeners();
  }
  
  // Theme management
  Future<void> setDarkMode(bool isDark) async {
    _isDarkMode = isDark;
    _themeMode = isDark ? ThemeMode.dark : ThemeMode.light;
    await StorageService.setDarkMode(isDark);
    notifyListeners();
  }
  
  Future<void> setThemeMode(ThemeMode mode) async {
    _themeMode = mode;
    
    // Update dark mode preference based on theme mode
    switch (mode) {
      case ThemeMode.dark:
        _isDarkMode = true;
        await StorageService.setDarkMode(true);
        break;
      case ThemeMode.light:
        _isDarkMode = false;
        await StorageService.setDarkMode(false);
        break;
      case ThemeMode.system:
        // Keep current preference for system mode
        break;
    }
    
    notifyListeners();
  }
  
  // Language management
  Future<void> setLanguage(String language) async {
    _currentLanguage = language;
    await StorageService.setLanguage(language);
    notifyListeners();
  }
  
  // Currency management
  Future<void> setCurrency(String currency) async {
    _currentCurrency = currency;
    await StorageService.setCurrency(currency);
    notifyListeners();
  }
  
  // Notifications management
  Future<void> setNotificationsEnabled(bool enabled) async {
    _notificationsEnabled = enabled;
    await StorageService.setNotificationsEnabled(enabled);
    notifyListeners();
  }
  
  void addNotification(AppNotification notification) {
    _notifications.insert(0, notification);
    
    // Keep only last 50 notifications
    if (_notifications.length > 50) {
      _notifications.removeRange(50, _notifications.length);
    }
    
    notifyListeners();
  }
  
  void removeNotification(String id) {
    _notifications.removeWhere((notification) => notification.id == id);
    notifyListeners();
  }
  
  void markNotificationAsRead(String id) {
    final index = _notifications.indexWhere((notification) => notification.id == id);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      notifyListeners();
    }
  }
  
  void clearAllNotifications() {
    _notifications.clear();
    notifyListeners();
  }
  
  int get unreadNotificationCount {
    return _notifications.where((notification) => !notification.isRead).length;
  }
  
  // Navigation management
  void setBottomNavIndex(int index) {
    _currentBottomNavIndex = index;
    notifyListeners();
  }
  
  // First launch management
  Future<void> completeFirstLaunch() async {
    _isFirstLaunch = false;
    await StorageService.setFirstLaunch(false);
    notifyListeners();
  }
  
  // Connectivity management
  void setOnlineStatus(bool isOnline) {
    _isOnline = isOnline;
    notifyListeners();
  }
  
  // Sync management
  Future<void> syncData() async {
    if (_isSyncing) return;
    
    _isSyncing = true;
    notifyListeners();
    
    try {
      // Simulate sync process
      await Future.delayed(const Duration(seconds: 2));
      
      _lastSyncTime = DateTime.now();
      
      // Add sync notification
      addNotification(AppNotification(
        id: 'sync_${DateTime.now().millisecondsSinceEpoch}',
        title: 'Data Synced',
        message: 'Your data has been synchronized successfully.',
        type: NotificationType.success,
        timestamp: DateTime.now(),
      ));
    } catch (e) {
      addNotification(AppNotification(
        id: 'sync_error_${DateTime.now().millisecondsSinceEpoch}',
        title: 'Sync Failed',
        message: 'Failed to synchronize data. Please try again.',
        type: NotificationType.error,
        timestamp: DateTime.now(),
      ));
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }
  
  // Utility methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  // Get formatted currency
  String formatCurrency(double amount) {
    switch (_currentCurrency) {
      case 'USD':
        return '\$${amount.toStringAsFixed(0)}';
      case 'EUR':
        return '€${amount.toStringAsFixed(0)}';
      case 'GBP':
        return '£${amount.toStringAsFixed(0)}';
      case 'JPY':
        return '¥${amount.toStringAsFixed(0)}';
      default:
        return '\$${amount.toStringAsFixed(0)}';
    }
  }
  
  // Get available languages
  List<Map<String, String>> getAvailableLanguages() {
    return [
      {'code': 'en', 'name': 'English'},
      {'code': 'es', 'name': 'Español'},
      {'code': 'fr', 'name': 'Français'},
      {'code': 'de', 'name': 'Deutsch'},
      {'code': 'it', 'name': 'Italiano'},
      {'code': 'pt', 'name': 'Português'},
      {'code': 'zh', 'name': '中文'},
      {'code': 'ja', 'name': '日本語'},
      {'code': 'ko', 'name': '한국어'},
      {'code': 'ar', 'name': 'العربية'},
    ];
  }
  
  // Get available currencies
  List<Map<String, String>> getAvailableCurrencies() {
    return [
      {'code': 'USD', 'name': 'US Dollar', 'symbol': '\$'},
      {'code': 'EUR', 'name': 'Euro', 'symbol': '€'},
      {'code': 'GBP', 'name': 'British Pound', 'symbol': '£'},
      {'code': 'JPY', 'name': 'Japanese Yen', 'symbol': '¥'},
      {'code': 'CAD', 'name': 'Canadian Dollar', 'symbol': 'C\$'},
      {'code': 'AUD', 'name': 'Australian Dollar', 'symbol': 'A\$'},
      {'code': 'CHF', 'name': 'Swiss Franc', 'symbol': 'CHF'},
      {'code': 'CNY', 'name': 'Chinese Yuan', 'symbol': '¥'},
    ];
  }
  
  // Reset app data
  Future<void> resetAppData() async {
    await StorageService.clearAllData();
    _isFirstLaunch = true;
    _isDarkMode = false;
    _themeMode = ThemeMode.system;
    _notificationsEnabled = true;
    _currentLanguage = 'en';
    _currentCurrency = 'USD';
    _currentBottomNavIndex = 0;
    _notifications.clear();
    notifyListeners();
  }
}

// Notification model
class AppNotification {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final DateTime timestamp;
  final bool isRead;
  final String? actionUrl;
  final Map<String, dynamic>? data;

  AppNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    this.isRead = false,
    this.actionUrl,
    this.data,
  });

  AppNotification copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    DateTime? timestamp,
    bool? isRead,
    String? actionUrl,
    Map<String, dynamic>? data,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      actionUrl: actionUrl ?? this.actionUrl,
      data: data ?? this.data,
    );
  }
}

enum NotificationType {
  info,
  success,
  warning,
  error,
}
