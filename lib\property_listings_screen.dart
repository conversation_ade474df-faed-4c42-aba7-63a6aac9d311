import 'package:flutter/material.dart';

class PropertyListingsScreen extends StatelessWidget {
  const PropertyListingsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Property Listings')),
      body: ListView.builder(
        itemCount: 10,
        itemBuilder: (context, index) => Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: ListTile(
            leading: Container(
              width: 60,
              height: 60,
              color: Colors.deepPurple[100],
              child: const Icon(Icons.home, size: 36, color: Colors.deepPurple),
            ),
            title: Text('Property ${index+1}'),
            subtitle: const Text('3 Bed, 2 Bath, 1500 sqft'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => Navigator.pushNamed(context, '/property_detail'),
          ),
        ),
      ),
    );
  }
}
