import 'package:flutter/material.dart';

class PropertyDetailScreen extends StatelessWidget {
  const PropertyDetailScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Property Detail')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: <PERSON><PERSON><PERSON>(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 200,
              width: double.infinity,
              color: Colors.deepPurple[100],
              child: const Icon(Icons.home, size: 100, color: Colors.deepPurple),
            ),
            const SizedBox(height: 20),
            const Text('Beautiful Family Home', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            const SizedBox(height: 10),
            const Text('3 Bed, 2 Bath, 1500 sqft'),
            const SizedBox(height: 10),
            const Text('Price: USD 350,000', style: TextStyle(fontSize: 20, color: Colors.deepPurple)),
            const SizedBox(height: 20),
            const Text('Description', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const Text('A beautiful family home located in a friendly neighborhood. Close to schools, parks, and shopping.'),
          ],
        ),
      ),
    );
  }
}
