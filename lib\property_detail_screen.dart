import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';
import 'providers/property_provider.dart';
import 'providers/user_provider.dart';
import 'models/property.dart';
import 'widgets/property_detail_widgets.dart';

class PropertyDetailScreen extends StatefulWidget {
  const PropertyDetailScreen({super.key});

  @override
  State<PropertyDetailScreen> createState() => _PropertyDetailScreenState();
}

class _PropertyDetailScreenState extends State<PropertyDetailScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  PageController _imagePageController = PageController();
  int _currentImageIndex = 0;
  String? _propertyId;
  Property? _property;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Get property ID from route arguments
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args != null && args != _propertyId) {
      _propertyId = args as String;
      _loadProperty();
    }
  }

  void _loadProperty() {
    if (_propertyId != null) {
      context.read<PropertyProvider>().loadPropertyById(_propertyId!);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _imagePageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Consumer2<PropertyProvider, UserProvider>(
          builder: (context, propertyProvider, userProvider, child) {
            _property = propertyProvider.selectedProperty;

            if (propertyProvider.isLoading) {
              return PropertyDetailWidgets.buildLoadingState();
            }

            if (propertyProvider.hasError || _property == null) {
              return PropertyDetailWidgets.buildErrorState(propertyProvider);
            }

            return CustomScrollView(
              slivers: [
                _buildAppBar(context, _property!, propertyProvider),
                SliverToBoxAdapter(
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildImageGallery(_property!),
                        PropertyDetailWidgets.buildPropertyInfo(context, _property!),
                        PropertyDetailWidgets.buildPropertyDetails(context, _property!),
                        PropertyDetailWidgets.buildAmenities(context, _property!),
                        PropertyDetailWidgets.buildDescription(context, _property!),
                        PropertyDetailWidgets.buildAgentInfo(context, _property!),
                        PropertyDetailWidgets.buildActionButtons(context, _property!, propertyProvider),
                        const SizedBox(height: 100), // Space for floating buttons
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
      floatingActionButton: _buildFloatingActionButtons(),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  Widget _buildAppBar(BuildContext context, Property property, PropertyProvider propertyProvider) {
    return SliverAppBar(
      expandedHeight: 0,
      floating: true,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(12),
        ),
        child: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: Icon(
              propertyProvider.isFavorite(property.id) ? Icons.favorite : Icons.favorite_border,
              color: propertyProvider.isFavorite(property.id) ? Colors.red : Colors.white,
            ),
            onPressed: () => propertyProvider.toggleFavorite(property.id),
          ),
        ),
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: const Icon(Icons.share, color: Colors.white),
            onPressed: () => _shareProperty(property),
          ),
        ),
      ],
    );
  }

  Widget _buildImageGallery(Property property) {
    if (property.images.isEmpty) {
      return Container(
        height: 300,
        color: Colors.grey[300],
        child: const Center(
          child: Icon(Icons.home, size: 80, color: Colors.grey),
        ),
      );
    }

    return Container(
      height: 300,
      child: Stack(
        children: [
          PageView.builder(
            controller: _imagePageController,
            onPageChanged: (index) {
              setState(() {
                _currentImageIndex = index;
              });
            },
            itemCount: property.images.length,
            itemBuilder: (context, index) {
              return CachedNetworkImage(
                imageUrl: property.images[index],
                fit: BoxFit.cover,
                width: double.infinity,
                placeholder: (context, url) => Container(
                  color: Colors.grey[300],
                  child: const Center(child: CircularProgressIndicator()),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[300],
                  child: const Center(
                    child: Icon(Icons.error, size: 50, color: Colors.grey),
                  ),
                ),
              );
            },
          ),

          // Image indicators
          if (property.images.length > 1)
            Positioned(
              bottom: 16,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: property.images.asMap().entries.map((entry) {
                  return Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _currentImageIndex == entry.key
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.5),
                    ),
                  );
                }).toList(),
              ),
            ),

          // Image counter
          if (property.images.length > 1)
            Positioned(
              top: 16,
              right: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${_currentImageIndex + 1}/${property.images.length}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    if (_property == null) return const SizedBox.shrink();

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FloatingActionButton(
          heroTag: "share",
          onPressed: () => _shareProperty(_property!),
          backgroundColor: Theme.of(context).colorScheme.secondary,
          child: const Icon(Icons.share),
        ),
        const SizedBox(height: 16),
        FloatingActionButton(
          heroTag: "favorite",
          onPressed: () {
            context.read<PropertyProvider>().toggleFavorite(_property!.id);
          },
          backgroundColor: context.read<PropertyProvider>().isFavorite(_property!.id)
              ? Colors.red
              : Colors.grey,
          child: Icon(
            context.read<PropertyProvider>().isFavorite(_property!.id)
                ? Icons.favorite
                : Icons.favorite_border,
          ),
        ),
      ],
    );
  }

  void _shareProperty(Property property) {
    Share.share(
      'Check out this amazing property: ${property.title}\n'
      'Location: ${property.location}\n'
      'Price: ${property.formattedPrice}\n'
      '${property.propertyDetails}\n\n'
      'Contact ${property.agentName} at ${property.agentPhone} for more information.',
      subject: 'Property: ${property.title}',
    );
  }
}
