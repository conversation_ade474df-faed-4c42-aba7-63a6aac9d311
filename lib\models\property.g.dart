// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'property.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PropertyAdapter extends TypeAdapter<Property> {
  @override
  final int typeId = 0;

  @override
  Property read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Property(
      id: fields[0] as String,
      title: fields[1] as String,
      description: fields[2] as String,
      price: fields[3] as double,
      location: fields[4] as String,
      address: fields[5] as String,
      bedrooms: fields[6] as int,
      bathrooms: fields[7] as int,
      area: fields[8] as double,
      images: (fields[9] as List).cast<String>(),
      type: fields[10] as PropertyType,
      status: fields[11] as PropertyStatus,
      latitude: fields[12] as double,
      longitude: fields[13] as double,
      agentName: fields[14] as String,
      agentPhone: fields[15] as String,
      agentEmail: fields[16] as String,
      createdAt: fields[17] as DateTime,
      updatedAt: fields[18] as DateTime,
      amenities: (fields[19] as List).cast<String>(),
      isFeatured: fields[20] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, Property obj) {
    writer
      ..writeByte(21)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.price)
      ..writeByte(4)
      ..write(obj.location)
      ..writeByte(5)
      ..write(obj.address)
      ..writeByte(6)
      ..write(obj.bedrooms)
      ..writeByte(7)
      ..write(obj.bathrooms)
      ..writeByte(8)
      ..write(obj.area)
      ..writeByte(9)
      ..write(obj.images)
      ..writeByte(10)
      ..write(obj.type)
      ..writeByte(11)
      ..write(obj.status)
      ..writeByte(12)
      ..write(obj.latitude)
      ..writeByte(13)
      ..write(obj.longitude)
      ..writeByte(14)
      ..write(obj.agentName)
      ..writeByte(15)
      ..write(obj.agentPhone)
      ..writeByte(16)
      ..write(obj.agentEmail)
      ..writeByte(17)
      ..write(obj.createdAt)
      ..writeByte(18)
      ..write(obj.updatedAt)
      ..writeByte(19)
      ..write(obj.amenities)
      ..writeByte(20)
      ..write(obj.isFeatured);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PropertyAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PropertyTypeAdapter extends TypeAdapter<PropertyType> {
  @override
  final int typeId = 1;

  @override
  PropertyType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PropertyType.house;
      case 1:
        return PropertyType.apartment;
      case 2:
        return PropertyType.condo;
      case 3:
        return PropertyType.townhouse;
      case 4:
        return PropertyType.villa;
      case 5:
        return PropertyType.land;
      default:
        return PropertyType.house;
    }
  }

  @override
  void write(BinaryWriter writer, PropertyType obj) {
    switch (obj) {
      case PropertyType.house:
        writer.writeByte(0);
        break;
      case PropertyType.apartment:
        writer.writeByte(1);
        break;
      case PropertyType.condo:
        writer.writeByte(2);
        break;
      case PropertyType.townhouse:
        writer.writeByte(3);
        break;
      case PropertyType.villa:
        writer.writeByte(4);
        break;
      case PropertyType.land:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PropertyTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PropertyStatusAdapter extends TypeAdapter<PropertyStatus> {
  @override
  final int typeId = 2;

  @override
  PropertyStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PropertyStatus.available;
      case 1:
        return PropertyStatus.sold;
      case 2:
        return PropertyStatus.pending;
      case 3:
        return PropertyStatus.rented;
      default:
        return PropertyStatus.available;
    }
  }

  @override
  void write(BinaryWriter writer, PropertyStatus obj) {
    switch (obj) {
      case PropertyStatus.available:
        writer.writeByte(0);
        break;
      case PropertyStatus.sold:
        writer.writeByte(1);
        break;
      case PropertyStatus.pending:
        writer.writeByte(2);
        break;
      case PropertyStatus.rented:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PropertyStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
