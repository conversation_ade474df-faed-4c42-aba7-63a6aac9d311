import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/property.dart';
import '../models/user.dart';

class StorageService {
  static const String _userBoxName = 'user_box';
  static const String _propertiesBoxName = 'properties_box';
  static const String _favoritesBoxName = 'favorites_box';
  static const String _preferencesBoxName = 'preferences_box';

  // Hive boxes
  static Box<User>? _userBox;
  static Box<Property>? _propertiesBox;
  static Box<String>? _favoritesBox;
  static Box<dynamic>? _preferencesBox;

  // SharedPreferences instance
  static SharedPreferences? _prefs;

  // Initialize storage
  static Future<void> init() async {
    await Hive.initFlutter();
    
    // Register adapters
    if (!Hive.isAdapterRegistered(0)) {
      Hive.registerAdapter(PropertyAdapter());
    }
    if (!Hive.isAdapterRegistered(1)) {
      Hive.registerAdapter(PropertyTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(2)) {
      Hive.registerAdapter(PropertyStatusAdapter());
    }
    if (!Hive.isAdapterRegistered(3)) {
      Hive.registerAdapter(UserAdapter());
    }
    if (!Hive.isAdapterRegistered(4)) {
      Hive.registerAdapter(UserRoleAdapter());
    }
    if (!Hive.isAdapterRegistered(5)) {
      Hive.registerAdapter(UserPreferencesAdapter());
    }

    // Open boxes
    _userBox = await Hive.openBox<User>(_userBoxName);
    _propertiesBox = await Hive.openBox<Property>(_propertiesBoxName);
    _favoritesBox = await Hive.openBox<String>(_favoritesBoxName);
    _preferencesBox = await Hive.openBox<dynamic>(_preferencesBoxName);

    // Initialize SharedPreferences
    _prefs = await SharedPreferences.getInstance();
  }

  // User management
  static Future<void> saveUser(User user) async {
    await _userBox?.put('current_user', user);
  }

  static User? getCurrentUser() {
    return _userBox?.get('current_user');
  }

  static Future<void> clearUser() async {
    await _userBox?.delete('current_user');
  }

  // Property caching
  static Future<void> cacheProperties(List<Property> properties) async {
    await _propertiesBox?.clear();
    for (final property in properties) {
      await _propertiesBox?.put(property.id, property);
    }
  }

  static List<Property> getCachedProperties() {
    return _propertiesBox?.values.toList() ?? [];
  }

  static Future<void> cacheProperty(Property property) async {
    await _propertiesBox?.put(property.id, property);
  }

  static Property? getCachedProperty(String id) {
    return _propertiesBox?.get(id);
  }

  static Future<void> clearPropertyCache() async {
    await _propertiesBox?.clear();
  }

  // Favorites management
  static Future<void> addToFavorites(String propertyId) async {
    await _favoritesBox?.put(propertyId, propertyId);
  }

  static Future<void> removeFromFavorites(String propertyId) async {
    await _favoritesBox?.delete(propertyId);
  }

  static bool isFavorite(String propertyId) {
    return _favoritesBox?.containsKey(propertyId) ?? false;
  }

  static List<String> getFavoriteIds() {
    return _favoritesBox?.values.toList() ?? [];
  }

  static List<Property> getFavoriteProperties() {
    final favoriteIds = getFavoriteIds();
    final allProperties = getCachedProperties();
    return allProperties.where((property) => favoriteIds.contains(property.id)).toList();
  }

  static Future<void> clearFavorites() async {
    await _favoritesBox?.clear();
  }

  // App preferences using SharedPreferences
  static Future<void> setFirstLaunch(bool isFirstLaunch) async {
    await _prefs?.setBool('first_launch', isFirstLaunch);
  }

  static bool isFirstLaunch() {
    return _prefs?.getBool('first_launch') ?? true;
  }

  static Future<void> setDarkMode(bool isDarkMode) async {
    await _prefs?.setBool('dark_mode', isDarkMode);
  }

  static bool isDarkMode() {
    return _prefs?.getBool('dark_mode') ?? false;
  }

  static Future<void> setNotificationsEnabled(bool enabled) async {
    await _prefs?.setBool('notifications_enabled', enabled);
  }

  static bool areNotificationsEnabled() {
    return _prefs?.getBool('notifications_enabled') ?? true;
  }

  static Future<void> setLanguage(String language) async {
    await _prefs?.setString('language', language);
  }

  static String getLanguage() {
    return _prefs?.getString('language') ?? 'en';
  }

  static Future<void> setCurrency(String currency) async {
    await _prefs?.setString('currency', currency);
  }

  static String getCurrency() {
    return _prefs?.getString('currency') ?? 'USD';
  }

  // Search history
  static Future<void> addSearchQuery(String query) async {
    final searches = getSearchHistory();
    searches.remove(query); // Remove if already exists
    searches.insert(0, query); // Add to beginning
    
    // Keep only last 10 searches
    if (searches.length > 10) {
      searches.removeRange(10, searches.length);
    }
    
    await _prefs?.setStringList('search_history', searches);
  }

  static List<String> getSearchHistory() {
    return _prefs?.getStringList('search_history') ?? [];
  }

  static Future<void> clearSearchHistory() async {
    await _prefs?.remove('search_history');
  }

  // Recently viewed properties
  static Future<void> addRecentlyViewed(String propertyId) async {
    final recent = getRecentlyViewed();
    recent.remove(propertyId); // Remove if already exists
    recent.insert(0, propertyId); // Add to beginning
    
    // Keep only last 20 properties
    if (recent.length > 20) {
      recent.removeRange(20, recent.length);
    }
    
    await _prefs?.setStringList('recently_viewed', recent);
  }

  static List<String> getRecentlyViewed() {
    return _prefs?.getStringList('recently_viewed') ?? [];
  }

  static List<Property> getRecentlyViewedProperties() {
    final recentIds = getRecentlyViewed();
    final allProperties = getCachedProperties();
    return recentIds
        .map((id) => allProperties.where((p) => p.id == id).firstOrNull)
        .where((property) => property != null)
        .cast<Property>()
        .toList();
  }

  static Future<void> clearRecentlyViewed() async {
    await _prefs?.remove('recently_viewed');
  }

  // Filter preferences
  static Future<void> saveFilterPreferences(Map<String, dynamic> filters) async {
    await _preferencesBox?.put('filters', filters);
  }

  static Map<String, dynamic> getFilterPreferences() {
    return Map<String, dynamic>.from(_preferencesBox?.get('filters', defaultValue: {}) ?? {});
  }

  static Future<void> clearFilterPreferences() async {
    await _preferencesBox?.delete('filters');
  }

  // App data management
  static Future<void> clearAllData() async {
    await _userBox?.clear();
    await _propertiesBox?.clear();
    await _favoritesBox?.clear();
    await _preferencesBox?.clear();
    await _prefs?.clear();
  }

  static Future<void> closeBoxes() async {
    await _userBox?.close();
    await _propertiesBox?.close();
    await _favoritesBox?.close();
    await _preferencesBox?.close();
  }

  // Data export/import for backup
  static Map<String, dynamic> exportUserData() {
    return {
      'user': getCurrentUser()?.toJson(),
      'favorites': getFavoriteIds(),
      'preferences': {
        'dark_mode': isDarkMode(),
        'notifications_enabled': areNotificationsEnabled(),
        'language': getLanguage(),
        'currency': getCurrency(),
      },
      'search_history': getSearchHistory(),
      'recently_viewed': getRecentlyViewed(),
      'filters': getFilterPreferences(),
    };
  }

  static Future<void> importUserData(Map<String, dynamic> data) async {
    // Import user
    if (data['user'] != null) {
      final user = User.fromJson(data['user']);
      await saveUser(user);
    }

    // Import favorites
    if (data['favorites'] != null) {
      await clearFavorites();
      for (final id in data['favorites']) {
        await addToFavorites(id);
      }
    }

    // Import preferences
    if (data['preferences'] != null) {
      final prefs = data['preferences'];
      await setDarkMode(prefs['dark_mode'] ?? false);
      await setNotificationsEnabled(prefs['notifications_enabled'] ?? true);
      await setLanguage(prefs['language'] ?? 'en');
      await setCurrency(prefs['currency'] ?? 'USD');
    }

    // Import search history
    if (data['search_history'] != null) {
      await _prefs?.setStringList('search_history', List<String>.from(data['search_history']));
    }

    // Import recently viewed
    if (data['recently_viewed'] != null) {
      await _prefs?.setStringList('recently_viewed', List<String>.from(data['recently_viewed']));
    }

    // Import filters
    if (data['filters'] != null) {
      await saveFilterPreferences(Map<String, dynamic>.from(data['filters']));
    }
  }
}
