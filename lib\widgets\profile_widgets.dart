import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../providers/user_provider.dart';
import '../providers/app_provider.dart';

class ProfileWidgets {
  static Widget buildStatsSection(BuildContext context, UserProvider userProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              context,
              'Favorites',
              '${userProvider.getFavoriteIds().length}',
              Icons.favorite,
              Colors.red,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              context,
              'Viewed',
              '12',
              Icons.visibility,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              context,
              'Saved',
              '8',
              Icons.bookmark,
              Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildStatCard(BuildContext context, String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  static Widget buildMenuSection(BuildContext context, UserProvider userProvider, AppProvider appProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Account',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                _buildMenuItem(
                  context,
                  Icons.person_outline,
                  'Edit Profile',
                  'Update your personal information',
                  () => _showEditProfileDialog(context, userProvider),
                ),
                _buildDivider(),
                _buildMenuItem(
                  context,
                  Icons.favorite_outline,
                  'My Favorites',
                  'View your saved properties',
                  () => Navigator.pushNamed(context, '/favorites'),
                ),
                _buildDivider(),
                _buildMenuItem(
                  context,
                  Icons.history,
                  'Recently Viewed',
                  'Properties you\'ve looked at',
                  () {
                    // Navigate to recently viewed
                  },
                ),
                _buildDivider(),
                _buildMenuItem(
                  context,
                  Icons.notifications_outline,
                  'Notifications',
                  'Manage your notification preferences',
                  () => _showNotificationSettings(context, appProvider),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static Widget buildPreferencesSection(BuildContext context, AppProvider appProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Preferences',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                _buildSwitchMenuItem(
                  context,
                  Icons.dark_mode_outlined,
                  'Dark Mode',
                  'Switch to dark theme',
                  appProvider.isDarkMode,
                  (value) => appProvider.setDarkMode(value),
                ),
                _buildDivider(),
                _buildMenuItem(
                  context,
                  Icons.language_outlined,
                  'Language',
                  appProvider.currentLanguage.toUpperCase(),
                  () => _showLanguageSelector(context, appProvider),
                ),
                _buildDivider(),
                _buildMenuItem(
                  context,
                  Icons.attach_money_outlined,
                  'Currency',
                  appProvider.currentCurrency,
                  () => _showCurrencySelector(context, appProvider),
                ),
                _buildDivider(),
                _buildSwitchMenuItem(
                  context,
                  Icons.notifications_active_outlined,
                  'Push Notifications',
                  'Receive property updates',
                  appProvider.notificationsEnabled,
                  (value) => appProvider.setNotificationsEnabled(value),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static Widget buildLogoutSection(BuildContext context, UserProvider userProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Account Actions',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                _buildMenuItem(
                  context,
                  Icons.help_outline,
                  'Help & Support',
                  'Get help with the app',
                  () {
                    // Show help
                  },
                ),
                _buildDivider(),
                _buildMenuItem(
                  context,
                  Icons.privacy_tip_outlined,
                  'Privacy Policy',
                  'Read our privacy policy',
                  () {
                    // Show privacy policy
                  },
                ),
                _buildDivider(),
                _buildMenuItem(
                  context,
                  Icons.logout,
                  'Logout',
                  'Sign out of your account',
                  () => _showLogoutDialog(context, userProvider),
                  isDestructive: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  static Widget _buildMenuItem(
    BuildContext context,
    IconData icon,
    String title,
    String subtitle,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isDestructive 
              ? Colors.red.withValues(alpha: 0.1)
              : Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: isDestructive ? Colors.red : Theme.of(context).colorScheme.primary,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          color: isDestructive ? Colors.red : null,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey[600],
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Colors.grey[400],
      ),
      onTap: onTap,
    );
  }

  static Widget _buildSwitchMenuItem(
    BuildContext context,
    IconData icon,
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey[600],
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
      ),
    );
  }

  static Widget _buildDivider() {
    return Divider(
      height: 1,
      indent: 16,
      endIndent: 16,
      color: Colors.grey[200],
    );
  }

  // Dialog methods
  static void _showEditProfileDialog(BuildContext context, UserProvider userProvider) {
    final nameController = TextEditingController(text: userProvider.currentUser?.name ?? '');
    final emailController = TextEditingController(text: userProvider.currentUser?.email ?? '');
    final phoneController = TextEditingController(text: userProvider.currentUser?.phone ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Profile'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              await userProvider.updateProfile(
                name: nameController.text,
                email: emailController.text,
                phone: phoneController.text,
              );
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  static void _showLogoutDialog(BuildContext context, UserProvider userProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              await userProvider.logout();
              Navigator.pop(context);
              Navigator.pushReplacementNamed(context, '/');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  static void _showLanguageSelector(BuildContext context, AppProvider appProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Language'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: appProvider.getAvailableLanguages().map((lang) {
            return ListTile(
              title: Text(lang['name']!),
              leading: Radio<String>(
                value: lang['code']!,
                groupValue: appProvider.currentLanguage,
                onChanged: (value) {
                  if (value != null) {
                    appProvider.setLanguage(value);
                    Navigator.pop(context);
                  }
                },
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  static void _showCurrencySelector(BuildContext context, AppProvider appProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Currency'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: appProvider.getAvailableCurrencies().map((currency) {
            return ListTile(
              title: Text('${currency['name']} (${currency['symbol']})'),
              leading: Radio<String>(
                value: currency['code']!,
                groupValue: appProvider.currentCurrency,
                onChanged: (value) {
                  if (value != null) {
                    appProvider.setCurrency(value);
                    Navigator.pop(context);
                  }
                },
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  static void _showNotificationSettings(BuildContext context, AppProvider appProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('Push Notifications'),
              subtitle: const Text('Receive property updates'),
              value: appProvider.notificationsEnabled,
              onChanged: (value) => appProvider.setNotificationsEnabled(value),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
