import 'dart:convert';
import 'package:dio/dio.dart';
import '../models/property.dart';
import '../models/user.dart';

class ApiService {
  static const String baseUrl = 'https://api.realestate.com/v1';
  late final Dio _dio;

  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors for logging and error handling
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (object) => print(object),
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onError: (error, handler) {
        print('API Error: ${error.message}');
        handler.next(error);
      },
    ));
  }

  // Property-related API calls
  Future<List<Property>> getProperties({
    int page = 1,
    int limit = 20,
    String? location,
    PropertyType? type,
    double? minPrice,
    double? maxPrice,
    String? searchQuery,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (location != null) queryParams['location'] = location;
      if (type != null) queryParams['type'] = type.toString().split('.').last;
      if (minPrice != null) queryParams['minPrice'] = minPrice;
      if (maxPrice != null) queryParams['maxPrice'] = maxPrice;
      if (searchQuery != null) queryParams['search'] = searchQuery;

      final response = await _dio.get('/properties', queryParameters: queryParams);
      
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['data'] ?? [];
        return data.map((json) => Property.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load properties');
      }
    } catch (e) {
      print('Error fetching properties: $e');
      // Return mock data for development
      return _getMockProperties();
    }
  }

  Future<Property?> getPropertyById(String id) async {
    try {
      final response = await _dio.get('/properties/$id');
      
      if (response.statusCode == 200) {
        return Property.fromJson(response.data['data']);
      } else {
        throw Exception('Property not found');
      }
    } catch (e) {
      print('Error fetching property: $e');
      // Return mock property for development
      return _getMockProperties().firstWhere(
        (property) => property.id == id,
        orElse: () => _getMockProperties().first,
      );
    }
  }

  Future<List<Property>> getFeaturedProperties() async {
    try {
      final response = await _dio.get('/properties/featured');
      
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['data'] ?? [];
        return data.map((json) => Property.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load featured properties');
      }
    } catch (e) {
      print('Error fetching featured properties: $e');
      // Return mock featured properties for development
      return _getMockProperties().where((p) => p.isFeatured).toList();
    }
  }

  Future<List<Property>> searchProperties(String query) async {
    try {
      final response = await _dio.get('/properties/search', queryParameters: {
        'q': query,
      });
      
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['data'] ?? [];
        return data.map((json) => Property.fromJson(json)).toList();
      } else {
        throw Exception('Search failed');
      }
    } catch (e) {
      print('Error searching properties: $e');
      // Return filtered mock data for development
      final mockProperties = _getMockProperties();
      return mockProperties.where((property) =>
        property.title.toLowerCase().contains(query.toLowerCase()) ||
        property.location.toLowerCase().contains(query.toLowerCase()) ||
        property.description.toLowerCase().contains(query.toLowerCase())
      ).toList();
    }
  }

  // User-related API calls
  Future<User?> getCurrentUser() async {
    try {
      final response = await _dio.get('/user/profile');
      
      if (response.statusCode == 200) {
        return User.fromJson(response.data['data']);
      } else {
        throw Exception('Failed to load user profile');
      }
    } catch (e) {
      print('Error fetching user: $e');
      // Return mock user for development
      return _getMockUser();
    }
  }

  Future<bool> updateUserProfile(User user) async {
    try {
      final response = await _dio.put('/user/profile', data: user.toJson());
      return response.statusCode == 200;
    } catch (e) {
      print('Error updating user profile: $e');
      return false;
    }
  }

  Future<bool> addToFavorites(String propertyId) async {
    try {
      final response = await _dio.post('/user/favorites', data: {
        'propertyId': propertyId,
      });
      return response.statusCode == 200;
    } catch (e) {
      print('Error adding to favorites: $e');
      return false;
    }
  }

  Future<bool> removeFromFavorites(String propertyId) async {
    try {
      final response = await _dio.delete('/user/favorites/$propertyId');
      return response.statusCode == 200;
    } catch (e) {
      print('Error removing from favorites: $e');
      return false;
    }
  }

  // Mock data for development
  List<Property> _getMockProperties() {
    return [
      Property(
        id: '1',
        title: 'Modern Family Home',
        description: 'Beautiful modern home with spacious rooms and a large backyard. Perfect for families looking for comfort and style.',
        price: 450000,
        location: 'Downtown',
        address: '123 Main Street, Downtown',
        bedrooms: 4,
        bathrooms: 3,
        area: 2500,
        images: [
          'https://images.unsplash.com/photo-1568605114967-8130f3a36994?w=800',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800',
          'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800',
        ],
        type: PropertyType.house,
        status: PropertyStatus.available,
        latitude: 40.7128,
        longitude: -74.0060,
        agentName: 'Sarah Johnson',
        agentPhone: '+****************',
        agentEmail: '<EMAIL>',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 5)),
        amenities: ['Swimming Pool', 'Garage', 'Garden', 'Fireplace'],
        isFeatured: true,
      ),
      Property(
        id: '2',
        title: 'Luxury Apartment',
        description: 'Stunning luxury apartment in the heart of the city with panoramic views and premium amenities.',
        price: 750000,
        location: 'City Center',
        address: '456 High Street, City Center',
        bedrooms: 2,
        bathrooms: 2,
        area: 1200,
        images: [
          'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800',
          'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800',
        ],
        type: PropertyType.apartment,
        status: PropertyStatus.available,
        latitude: 40.7589,
        longitude: -73.9851,
        agentName: 'Michael Chen',
        agentPhone: '+****************',
        agentEmail: '<EMAIL>',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
        amenities: ['Gym', 'Concierge', 'Rooftop Terrace', 'Parking'],
        isFeatured: true,
      ),
      Property(
        id: '3',
        title: 'Cozy Townhouse',
        description: 'Charming townhouse in a quiet neighborhood with easy access to schools and parks.',
        price: 320000,
        location: 'Suburbia',
        address: '789 Oak Avenue, Suburbia',
        bedrooms: 3,
        bathrooms: 2,
        area: 1800,
        images: [
          'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800',
        ],
        type: PropertyType.townhouse,
        status: PropertyStatus.available,
        latitude: 40.6892,
        longitude: -74.0445,
        agentName: 'Emily Davis',
        agentPhone: '+****************',
        agentEmail: '<EMAIL>',
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
        updatedAt: DateTime.now().subtract(const Duration(days: 10)),
        amenities: ['Patio', 'Storage', 'Laundry Room'],
        isFeatured: false,
      ),
    ];
  }

  User _getMockUser() {
    return User(
      id: 'user_1',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+****************',
      profileImage: null,
      role: UserRole.buyer,
      createdAt: DateTime.now().subtract(const Duration(days: 365)),
      lastLoginAt: DateTime.now(),
      favoritePropertyIds: ['1', '2'],
      preferences: UserPreferences(
        darkMode: false,
        notifications: true,
        currency: 'USD',
        language: 'en',
        minPrice: 200000,
        maxPrice: 800000,
        preferredLocations: ['Downtown', 'City Center'],
        preferredPropertyTypes: [PropertyType.house, PropertyType.apartment],
      ),
    );
  }
}
